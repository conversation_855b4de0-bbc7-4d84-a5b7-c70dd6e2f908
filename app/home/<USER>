import {getUserOrganizations} from "@/app/(repository)/OrganizationRepository";
import {getCurrentUser} from "@/app/(repository)/UserRepository";
import {redirect} from "next/navigation";
import {routes} from "@/utils/routes";

export default async function Home() {
  const user = await getCurrentUser();
  if (!user) {
    redirect(routes.landing);
  }
  const orgs = await getUserOrganizations(user.id);
  console.log(orgs);

  if (orgs.length === 0) {
    return <div>Create an organization</div>;
  }
  return <div>Home</div>;
}

