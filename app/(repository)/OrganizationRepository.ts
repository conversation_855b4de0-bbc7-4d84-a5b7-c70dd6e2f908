"use server";

import {createClient} from "@/utils/supabase/server";
import {getCurrentUser} from "@/app/(repository)/UserRepository";

export async function getUserOrganizations(userId: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("organization_member")
    .select("*")
    .eq("user_id", userId);

  if (error) {
    console.error(error);
    return [];
  }

  return [];
}

export async function createOrganization(formData: FormData) {
  console.log("createOrganization", formData);
  const name = formData.get("name") as string;

  // Get the current user
  const user = await getCurrentUser();
  if (!user) {
    console.error("No authenticated user found");
    return { error: "Authentication required" };
  }

  const supabase = await createClient();

  // Create the organization and get the ID
  const { data: organizationData, error: orgError } = await supabase
    .from("organization")
    .insert({ name })
    .select("id")
    .single();

  if (orgError) {
    console.error("Error creating organization:", orgError);
    return { error: "Failed to create organization" };
  }

  // Add the user to the organization_member table with "owner" role
  const { error: memberError } = await supabase
    .from("organization_member")
    .insert({
      user_id: user.id,
      organization_id: organizationData.id,
      role: "owner"
    });

  if (memberError) {
    console.error("Error adding user to organization:", memberError);
    return { error: "Failed to add user to organization" };
  }

  return { success: true, organizationId: organizationData.id };
}
